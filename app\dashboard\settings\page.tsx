"use client"

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/hooks/use-toast"
import { useTheme } from "next-themes"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Palette,
  Bell,
  User,
  Check,
  Shield,
  Globe,
  Smartphone,
  Mail,
  Lock,
  Eye,
  Moon,
  Sun,
  Monitor,
  Sparkles,
  Settings as <PERSON>ting<PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

// Schemas
const appearanceFormSchema = z.object({
  theme: z.enum(["light", "dark", "system"], {
    required_error: "Please select a theme.",
  }),
})

const profileFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(50, "Name must not exceed 50 characters."),
  email: z.string().email("Please enter a valid email address."),
  bio: z.string().max(160, "Bio must not exceed 160 characters.").optional(),
  language: z.string({ required_error: "Please select a language." }),
})

const notificationFormSchema = z.object({
  marketing: z.boolean().default(false),
  security: z.boolean().default(true),
  updates: z.boolean().default(true),
  mobile: z.boolean().default(false),
  email: z.boolean().default(true),
})

// Form Values Types
type AppearanceFormValues = z.infer<typeof appearanceFormSchema>
type ProfileFormValues = z.infer<typeof profileFormSchema>
type NotificationFormValues = z.infer<typeof notificationFormSchema>

// Theme Preview Component
function ThemePreview({ theme }: { theme: "light" | "dark" | "system" }) {
  if (theme === "system") {
    return (
      <div className="w-full h-20 rounded-lg border-2 border-neutral-200 dark:border-neutral-700 bg-background flex overflow-hidden">
        <div className="w-1/2 h-full bg-white flex items-center justify-center">
          <Sun className="w-4 h-4 text-neutral-600" />
        </div>
        <div className="w-1/2 h-full bg-neutral-950 flex items-center justify-center">
          <Moon className="w-4 h-4 text-neutral-400" />
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "w-full h-20 rounded-lg border-2 flex items-center justify-center",
      theme === "light"
        ? "bg-white border-neutral-200 text-neutral-600"
        : "bg-neutral-950 border-neutral-700 text-neutral-400"
    )}>
      {theme === "light" ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
    </div>
  )
}


// Theme preview component
function ThemePreview({ theme }: { theme: "light" | "dark" | "system" }) {
  return (
    <div className="relative overflow-hidden rounded-lg border-2 border-transparent p-4 hover:border-neutral-300 dark:hover:border-neutral-600 transition-colors">
      <div className={cn(
        "rounded-md p-3 space-y-2",
        theme === "light" && "bg-white border border-neutral-200",
        theme === "dark" && "bg-neutral-900 border border-neutral-700",
        theme === "system" && "bg-gradient-to-br from-white to-neutral-100 dark:from-neutral-900 dark:to-neutral-800 border border-neutral-200 dark:border-neutral-700"
      )}>
        <div className={cn(
          "h-2 w-16 rounded",
          theme === "light" && "bg-neutral-200",
          theme === "dark" && "bg-neutral-700",
          theme === "system" && "bg-gradient-to-r from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-600"
        )} />
        <div className={cn(
          "h-1.5 w-12 rounded",
          theme === "light" && "bg-neutral-100",
          theme === "dark" && "bg-neutral-800",
          theme === "system" && "bg-gradient-to-r from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-700"
        )} />
        <div className={cn(
          "h-1.5 w-8 rounded",
          theme === "light" && "bg-neutral-100",
          theme === "dark" && "bg-neutral-800",
          theme === "system" && "bg-gradient-to-r from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-700"
        )} />
      </div>
    </div>
  )
}

export default function SettingsPage() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const appearanceForm = useForm<AppearanceFormValues>({
    resolver: zodResolver(appearanceFormSchema),
    defaultValues: {
      theme: (theme as "light" | "dark" | "system") || "system",
    },
  })

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "John Doe",
      email: "<EMAIL>",
      bio: "Legal professional specializing in corporate law.",
      language: "en",
    },
  })

  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      marketing: false,
      security: true,
      updates: true,
    },
  })

  function onAppearanceSubmit(data: AppearanceFormValues) {
    setTheme(data.theme)
    toast({
      title: "Theme Updated",
      description: `Switched to ${data.theme} theme.`,
    })
  }

  function onProfileSubmit(data: ProfileFormValues) {
    toast({
      title: "Profile Updated",
      description: "Your profile information has been saved.",
    })
  }

  function onNotificationSubmit(data: NotificationFormValues) {
    toast({
      title: "Notifications Updated",
      description: "Your notification settings have been saved.",
    })
  }

  if (!mounted) {
    return null
  }
  
  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
          className="mb-12"
        >
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2.5 rounded-xl bg-gradient-to-br from-neutral-100 to-neutral-50 dark:from-neutral-800 dark:to-neutral-900 border border-neutral-200/50 dark:border-neutral-700/50">
              <SettingsIcon className="w-6 h-6 text-neutral-700 dark:text-neutral-200" />
            </div>
            <div>
              <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
                Settings
              </h1>
              <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
                Customize your workspace and preferences
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 auto-rows-fr">
          {/* Profile Card */}
          <motion.div
            variants={bentoVariants}
            initial="hidden"
            animate="visible"
            custom={1}
            className="lg:col-span-1"
          >
            <Card className="h-full bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-800">
                    <User className="w-5 h-5 text-neutral-700 dark:text-neutral-300" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-medium">Profile</CardTitle>
                    <CardDescription>Update your personal information.</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Form {...profileForm}>
                  <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                    <div className="grid sm:grid-cols-2 gap-4">
                      <FormField
                        control={profileForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="John Doe" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email Address</FormLabel>
                            <FormControl>
                              <Input {...field} type="email" placeholder="<EMAIL>" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <FormField
                      control={profileForm.control}
                      name="bio"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bio</FormLabel>
                          <FormControl>
                            <Textarea {...field} placeholder="Tell us about yourself..." />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={profileForm.control}
                      name="language"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Language</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a language" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="en">English</SelectItem>
                              <SelectItem value="es">Spanish</SelectItem>
                              <SelectItem value="fr">French</SelectItem>
                              <SelectItem value="de">German</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit">Save Changes</Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Appearance Card */}
          <motion.div
            variants={bentoVariants}
            initial="hidden"
            animate="visible"
            custom={2}
            className="lg:col-span-1"
          >
            <Card className="h-full flex flex-col bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-800">
                    <Palette className="w-5 h-5 text-neutral-700 dark:text-neutral-300" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-medium">Appearance</CardTitle>
                    <CardDescription>Customize the look and feel.</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="flex-grow">
                <Form {...appearanceForm}>
                  <form onSubmit={appearanceForm.handleSubmit(onAppearanceSubmit)} className="h-full flex flex-col justify-between">
                    <FormField
                      control={appearanceForm.control}
                      name="theme"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Theme</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              value={field.value}
                              className="grid grid-cols-3 gap-4 pt-2"
                            >
                              {(new Array('light', 'dark', 'system') as any).map((themeValue) => (
                                <FormItem key={themeValue}>
                                  <FormLabel className="cursor-pointer relative">
                                    <FormControl>
                                      <RadioGroupItem value={themeValue} className="sr-only" />
                                    </FormControl>
                                    <ThemePreview theme={themeValue} />
                                    <div className="mt-2 text-center text-sm capitalize">
                                      {themeValue}
                                    </div>
                                    {field.value === themeValue && (
                                      <div className="absolute inset-0 rounded-md border-2 border-primary ring-2 ring-primary/50" />
                                    )}
                                  </FormLabel>
                                </FormItem>
                              ))}
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" className="w-full mt-6">Apply Theme</Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Notifications Card */}
          <motion.div
            variants={bentoVariants}
            initial="hidden"
            animate="visible"
            custom={3}
            className="lg:col-span-2"
          >
            <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-800">
                    <Bell className="w-5 h-5 text-neutral-700 dark:text-neutral-300" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-medium">Notifications</CardTitle>
                    <CardDescription>Configure how you receive updates.</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Form {...notificationForm}>
                  <form onSubmit={notificationForm.handleSubmit(onNotificationSubmit)} className="space-y-4">
                    <FormField
                      control={notificationForm.control}
                      name="security"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between p-4 rounded-lg bg-white/50 dark:bg-neutral-800/30 border border-neutral-200 dark:border-neutral-700">
                          <div>
                            <FormLabel>Security Alerts</FormLabel>
                            <FormDescription>
                              Notify about security events and login attempts.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="updates"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between p-4 rounded-lg bg-white/50 dark:bg-neutral-800/30 border border-neutral-200 dark:border-neutral-700">
                          <div>
                            <FormLabel>Product Updates</FormLabel>
                            <FormDescription>
                              Receive news about new features and improvements.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="marketing"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between p-4 rounded-lg bg-white/50 dark:bg-neutral-800/30 border border-neutral-200 dark:border-neutral-700">
                          <div>
                            <FormLabel>Marketing</FormLabel>
                            <FormDescription>
                              Receive emails about tips, tutorials, and offers.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <Button type="submit">Save Preferences</Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}