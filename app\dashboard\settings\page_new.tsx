"use client"

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/hooks/use-toast"
import { useTheme } from "next-themes"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Palette, 
  Bell, 
  User, 
  Check, 
  Shield, 
  Globe, 
  Smartphone, 
  Mail, 
  Lock, 
  Eye,
  Moon,
  Sun,
  Monitor,
  Sparkles,
  Settings as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
} from "lucide-react"
import { motion } from "framer-motion"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

// Schemas
const appearanceFormSchema = z.object({
  theme: z.enum(["light", "dark", "system"], {
    required_error: "Please select a theme.",
  }),
})

const profileFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(50, "Name must not exceed 50 characters."),
  email: z.string().email("Please enter a valid email address."),
  bio: z.string().max(160, "Bio must not exceed 160 characters.").optional(),
  language: z.string({ required_error: "Please select a language." }),
})

const notificationFormSchema = z.object({
  marketing: z.boolean().default(false),
  security: z.boolean().default(true),
  updates: z.boolean().default(true),
  mobile: z.boolean().default(false),
  email: z.boolean().default(true),
})

// Form Values Types
type AppearanceFormValues = z.infer<typeof appearanceFormSchema>
type ProfileFormValues = z.infer<typeof profileFormSchema>
type NotificationFormValues = z.infer<typeof notificationFormSchema>

// Theme Preview Component
function ThemePreview({ theme }: { theme: "light" | "dark" | "system" }) {
  if (theme === "system") {
    return (
      <div className="w-full h-20 rounded-lg border-2 border-neutral-200 dark:border-neutral-700 bg-background flex overflow-hidden">
        <div className="w-1/2 h-full bg-white flex items-center justify-center">
          <Sun className="w-4 h-4 text-neutral-600" />
        </div>
        <div className="w-1/2 h-full bg-neutral-950 flex items-center justify-center">
          <Moon className="w-4 h-4 text-neutral-400" />
        </div>
      </div>
    )
  }
  
  return (
    <div className={cn(
      "w-full h-20 rounded-lg border-2 flex items-center justify-center",
      theme === "light" 
        ? "bg-white border-neutral-200 text-neutral-600" 
        : "bg-neutral-950 border-neutral-700 text-neutral-400"
    )}>
      {theme === "light" ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
    </div>
  )
}

export default function SettingsPage() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const appearanceForm = useForm<AppearanceFormValues>({
    resolver: zodResolver(appearanceFormSchema),
    defaultValues: {
      theme: (theme as "light" | "dark" | "system") || "system",
    },
  })

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "John Doe",
      email: "<EMAIL>",
      bio: "Legal professional specializing in corporate law.",
      language: "en",
    },
  })

  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      marketing: false,
      security: true,
      updates: true,
      mobile: false,
      email: true,
    },
  })

  function onAppearanceSubmit(data: AppearanceFormValues) {
    setTheme(data.theme)
    toast({
      title: "Theme Updated",
      description: `Switched to ${data.theme} theme.`,
    })
  }

  function onProfileSubmit(data: ProfileFormValues) {
    toast({
      title: "Profile Updated",
      description: "Your profile information has been saved.",
    })
  }

  function onNotificationSubmit(data: NotificationFormValues) {
    toast({
      title: "Notifications Updated",
      description: "Your notification settings have been saved.",
    })
  }

  if (!mounted) {
    return null
  }

  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
          className="mb-12"
        >
          <div className="flex items-center gap-4 mb-6">
            <motion.div 
              className="p-3 rounded-xl bg-gradient-to-br from-neutral-100 to-neutral-50 dark:from-neutral-800 dark:to-neutral-900 border border-neutral-200/50 dark:border-neutral-700/50 shadow-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <SettingsIcon className="w-7 h-7 text-neutral-700 dark:text-neutral-200" />
            </motion.div>
            <div>
              <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
                Settings
              </h1>
              <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
                Customize your workspace and preferences
              </p>
            </div>
          </div>
        </motion.div>

        {/* Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          
          {/* Profile Card - Large */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}
            className="md:col-span-2 lg:col-span-2"
          >
            <Card className="h-full bg-white/60 dark:bg-neutral-900/60 backdrop-blur-2xl border-neutral-200/30 dark:border-neutral-800/30 shadow-xl shadow-neutral-900/5 dark:shadow-neutral-900/20">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-4">
                  <motion.div 
                    className="p-3 rounded-xl bg-gradient-to-br from-blue-100 to-blue-50 dark:from-blue-900/30 dark:to-blue-800/30 border border-blue-200/50 dark:border-blue-700/50"
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                    <UserCircle className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </motion.div>
                  <div>
                    <CardTitle className="text-2xl font-light text-neutral-900 dark:text-white">Profile Settings</CardTitle>
                    <CardDescription className="text-neutral-600 dark:text-neutral-400">
                      Manage your personal information and preferences
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <Form {...profileForm}>
                  <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={profileForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Full Name</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter your full name" 
                                className="bg-white/50 dark:bg-neutral-800/50 border-neutral-200/50 dark:border-neutral-700/50 focus:border-neutral-400 dark:focus:border-neutral-500"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Email Address</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="<EMAIL>" 
                                className="bg-white/50 dark:bg-neutral-800/50 border-neutral-200/50 dark:border-neutral-700/50 focus:border-neutral-400 dark:focus:border-neutral-500"
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={profileForm.control}
                      name="bio"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium text-neutral-700 dark:text-neutral-300">Bio</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Tell us about your legal expertise and background..."
                              className="resize-none bg-white/50 dark:bg-neutral-800/50 border-neutral-200/50 dark:border-neutral-700/50 focus:border-neutral-400 dark:focus:border-neutral-500 min-h-[100px]"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400">
                            Brief description of your professional background
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="flex justify-end">
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Button 
                          type="submit" 
                          className="bg-neutral-900 hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-100 text-white dark:text-neutral-900 px-8"
                        >
                          <Check className="w-4 h-4 mr-2" />
                          Save Changes
                        </Button>
                      </motion.div>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </motion.div>
